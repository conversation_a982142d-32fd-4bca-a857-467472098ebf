This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=pdflatex 2025.7.7)  9 JUL 2025 13:28
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**cotangente_replica_real.tex
(./cotangente_replica_real.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-06-09>
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/standalone/standalone.cls
Document Class: standalone 2025/02/22 v1.5a Class to compile TeX sub-files standalone
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/xkeyval/keyval.tex))
\XKV@depth=\count275
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\sa@internal=\count276
\c@sapage=\count277
 (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/standalone/standalone.cfg
File: standalone.cfg 2025/02/22 v1.5a Default configuration file for 'standalone' class
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count278
\c@section=\count279
\c@subsection=\count280
\c@subsubsection=\count281
\c@paragraph=\count282
\c@subparagraph=\count283
\c@figure=\count284
\c@table=\count285
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
)
\sa@box=\box53
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen149
\pgfutil@tempdimb=\dimen150
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box54
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen151
\Gin@req@width=\dimen152
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks22
))
\pgf@x=\dimen153
\pgf@y=\dimen154
\pgf@xa=\dimen155
\pgf@ya=\dimen156
\pgf@xb=\dimen157
\pgf@yb=\dimen158
\pgf@xc=\dimen159
\pgf@yc=\dimen160
\pgf@xd=\dimen161
\pgf@yd=\dimen162
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count286
\c@pgf@countb=\count287
\c@pgf@countc=\count288
\c@pgf@countd=\count289
\t@pgf@toka=\toks23
\t@pgf@tokb=\toks24
\t@pgf@tokc=\toks25
\pgf@sys@id@count=\count290
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count291
\pgfsyssoftpath@bigbuffer@items=\count292
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen163
\pgfmath@count=\count293
\pgfmath@box=\box55
\pgfmath@toks=\toks26
\pgfmath@stack@operand=\toks27
\pgfmath@stack@operation=\toks28
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count294
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen164
\pgf@picmaxx=\dimen165
\pgf@picminy=\dimen166
\pgf@picmaxy=\dimen167
\pgf@pathminx=\dimen168
\pgf@pathmaxx=\dimen169
\pgf@pathminy=\dimen170
\pgf@pathmaxy=\dimen171
\pgf@xx=\dimen172
\pgf@xy=\dimen173
\pgf@yx=\dimen174
\pgf@yy=\dimen175
\pgf@zx=\dimen176
\pgf@zy=\dimen177
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen178
\pgf@path@lasty=\dimen179
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen180
\pgf@shorten@start@additional=\dimen181
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box56
\pgf@hbox=\box57
\pgf@layerbox@main=\box58
\pgf@picture@serial@count=\count295
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen182
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen183
\pgf@pt@y=\dimen184
\pgf@pt@temp=\dimen185
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen186
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen187
\pgf@sys@shading@range@num=\count296
\pgf@shadingcount=\count297
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box59
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box60
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen188
\pgf@nodesepend=\dimen189
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen190
\pgffor@skip=\dimen191
\pgffor@stack=\toks29
\pgffor@toks=\toks30
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count298
\pgfplotmarksize=\dimen192
)
\tikz@lastx=\dimen193
\tikz@lasty=\dimen194
\tikz@lastxsaved=\dimen195
\tikz@lastysaved=\dimen196
\tikz@lastmovetox=\dimen197
\tikz@lastmovetoy=\dimen198
\tikzleveldistance=\dimen199
\tikzsiblingdistance=\dimen256
\tikz@figbox=\box61
\tikz@figbox@bg=\box62
\tikz@tempbox=\box63
\tikz@tempbox@bg=\box64
\tikztreelevel=\count299
\tikznumberofchildren=\count300
\tikznumberofcurrentchild=\count301
\tikz@fig@count=\count302
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count303
\pgfmatrixcurrentcolumn=\count304
\pgf@matrix@numberofcolumns=\count305
)
\tikz@expandcount=\count306
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgfplots/pgfplots.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks31
\t@pgfplots@tokb=\toks32
\t@pgfplots@tokc=\toks33
\pgfplots@tmpa=\dimen257
\c@pgfplots@coordindex=\count307
\c@pgfplots@scanlineindex=\count308
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_loader.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks34
\t@pgf@tokb=\toks35
\t@pgf@tokc=\toks36
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_pgfutil-common-lists.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructureext.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.code.tex
\c@pgfplotsarray@tmp=\count309
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.code.tex
\c@pgfplotstable@counta=\count310
\t@pgfplotstable@a=\toks37
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfshading.code.tex
\c@pgfplotslibrarysurf@no=\count311
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfshading.pgfsys-pdftex.def))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.tex
\pgfdecoratedcompleteddistance=\dimen258
\pgfdecoratedremainingdistance=\dimen259
\pgfdecoratedinputsegmentcompleteddistance=\dimen260
\pgfdecoratedinputsegmentremainingdistance=\dimen261
\pgf@decorate@distancetomove=\dimen262
\pgf@decorate@repeatstate=\count312
\pgfdecorationsegmentamplitude=\dimen263
\pgfdecorationsegmentlength=\dimen264
)
\tikz@lib@dec@box=\box65
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.pathmorphing.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorations.pathmorphing.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.pathreplacing.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorations.pathreplacing.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.contourlua.code.tex)
\pgfplots@numplots=\count313
\pgfplots@xmin@reg=\dimen265
\pgfplots@xmax@reg=\dimen266
\pgfplots@ymin@reg=\dimen267
\pgfplots@ymax@reg=\dimen268
\pgfplots@zmin@reg=\dimen269
\pgfplots@zmax@reg=\dimen270
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2025-06-09 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count314
)
No file cotangente_replica_real.aux.
\openout1 = `cotangente_replica_real.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checki